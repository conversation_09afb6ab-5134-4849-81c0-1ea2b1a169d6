import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cal/features/subscriptions/presentation/bloc/subscription_bloc.dart';
import 'package:cal/features/subscriptions/presentation/pages/free_hook_page.dart';
import 'package:cal/features/subscriptions/presentation/pages/payment_page.dart';
import 'package:cal/features/subscriptions/presentation/pages/exit_promo_page.dart';
import 'package:cal/core/di/injection.dart';

class PaymentFlow extends StatelessWidget {
  const PaymentFlow({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<SubscriptionBloc>()..add(const InitializeSubscriptions()),
      child: Navigator(
        initialRoute: '/',
        onGenerateRoute: (settings) {
          Widget page;
          switch (settings.name) {
            case '/':
              page = const PaymentPage();
              break;
            case '/payment':
              page = const PaymentPage();
              break;
            case '/exit_promo':
              page = const ExitPromoPage();
              break;
            default:
              page = const PaymentPage();
              break;
          }
          return MaterialPageRoute(builder: (context) => page, settings: settings);
        },
      ),
    );
  }
}
