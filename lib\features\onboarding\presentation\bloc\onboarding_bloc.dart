import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cal/common/consts/app_keys.dart';
import 'package:cal/common/utils/notifications.dart';
import 'package:cal/common/utils/shared_preferences_helper.dart';
import 'package:cal/core/local_models/user_model/user_model.dart';
import 'package:cal/core/mobile_id_helper.dart';
import 'package:cal/features/onboarding/data/models/onboarding_model.dart';
import 'package:cal/features/onboarding/domain/usecases/get_user_local_user_case.dart';
import 'package:cal/features/onboarding/domain/usecases/local_delete_clear_user_use_case.dart';
import 'package:cal/features/onboarding/domain/usecases/local_save_update_user_use_case.dart';
import 'package:cal/features/onboarding/domain/usecases/submit_onboarding_usecase.dart';
import 'package:cal/features/onboarding/enums.dart';
import 'package:cal/features/onboarding/models/onboarding_screen.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'dart:developer';


part 'onboarding_event.dart';

part 'onboarding_state.dart';

@injectable
class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  final PageController pageController = PageController();
  final SubmitOnboardingUsecase submitOnboardingUsecase;

  final GetUserLocalUserCase getUserLocalUserCase;
  final LocalDeleteClearUserUseCase localDeleteClearUserUseCase;
  final LocalSaveUpdateUserUseCase localSaveUpdateUserUseCase;

  // Define all possible screens in the onboarding flow
  static List<OnboardingScreen> allScreens = [
    OnboardingScreen(id: 'gender', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'gender')),
    OnboardingScreen(id: 'frequency', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'frequency')),
    OnboardingScreen(id: 'where_did_hear_of_us', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'where_did_hear_of_us')),
    OnboardingScreen(
        id: 'did_use_other_app_content', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'did_use_other_app_content')),
    OnboardingScreen(id: 'weight_chart_content', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'weight_chart_content')),
    OnboardingScreen(id: 'height_weight', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'height_weight')),
    OnboardingScreen(id: 'birth_date', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'birth_date')),
    OnboardingScreen(id: 'goal_selection', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'goal_selection')),
    OnboardingScreen(id: 'target_weight', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'target_weight')),
    OnboardingScreen(id: 'weight_visualization', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'weight_visualization')),
    OnboardingScreen(id: 'weight_change_rate', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'weight_change_rate')),
    OnboardingScreen(id: 'with_orange_ai_content', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'with_orange_ai_content')),
    OnboardingScreen(
        id: 'what_doesnot_make_you_commit', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'what_doesnot_make_you_commit')),
    OnboardingScreen(id: 'what_is_your_diet', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'what_is_your_diet')),
    OnboardingScreen(
        id: 'what_you_want_to_achieve', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'what_you_want_to_achieve')),
    OnboardingScreen(
        id: 'achieve_goal_visualaization', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'achieve_goal_visualaization')),
    // OnboardingScreen(id: 'meals_time_content', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'meals_time_content')), // Removed as per user request
    OnboardingScreen(id: 'activate_notifications', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'activate_notifications')),
    // OnboardingScreen(id: 'apple_health_content'),
    OnboardingScreen(id: 'add_cals_again', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'add_cals_again')),
    // OnboardingScreen(id: 'add_yesterday_cals_content'),
    // OnboardingScreen(id: 'referral_code_content', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'referral_code_content')),
    OnboardingScreen(id: 'welcome', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'welcome')),
    OnboardingScreen(id: 'processing_info', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'processing_info')),
    OnboardingScreen(id: 'result', shouldShow: (state) => _shouldShowWeightRelatedScreen(state, 'result')),
    // OnboardingScreen(id: 'free_trail_content'),
  ];

  static List<String> settingsScreensId = [
    'frequency',
    'height_weight',
    'goal_selection',
    'target_weight',
    'weight_change_rate',
    'processing_info',
    'result',
  ];

  static List<String> specialScreensId = [
    'target_weight',
    'weight_visualization',
    'weight_change_rate',
  ];

  // Static method to determine if weight-related screens should be shown
  static bool _shouldShowWeightRelatedScreen(OnboardingState state, String screenId) {
    if (state.shouldShowInSettingsScreens ?? false) {
      return settingsScreensId.contains(screenId);
    } else {
      if (specialScreensId.contains(screenId)) {
        return (state.goal != null && state.goal != Goal.maintenance);
      }
      return true;
    }
  }

  OnboardingBloc({
    required this.submitOnboardingUsecase,
    required this.localDeleteClearUserUseCase,
    required this.localSaveUpdateUserUseCase,
    required this.getUserLocalUserCase,
  }) : super(const OnboardingState()) {
    on<InitializeBloc>(_initializeBloc);
    on<UpdateGender>(_updateGender);
    on<UpdateExerciseFrequency>(_updateExerciseFrequency);
    on<UpdateWhereDidYouHeardAboutUs>(_updateWhereDidYouHeardAboutUs);
    on<UpdateHasUsedOtherApps>(_updateHasUsedOtherApps);
    on<UpdateHeight>(_updateHeight);
    on<UpdateWeight>(_updateWeight);
    on<UpdateBirthDay>(_updateBirthDay);
    on<UpdateBirthMonth>(_updateBirthMonth);
    on<UpdateBirthYear>(_updateBirthYear);
    on<UpdateWeightChangeRate>(_updateWeightLossRate);
    on<UpdateGoal>(_updateGoal);
    on<UpdateMealtime>(_updateMealtime);
    on<UpdateNutritionValues>(_updateNutritionValues);
    on<UpdateWhatYouWantToAchieve>(_updateWhatYouWantToAchieve);
    on<UpdateWhatMakesYouDoesnotCommit>(_updateWhatMakesYouDoesnotCommit);
    on<UpdateWhatIsYourDiet>(_updateUpdateWhatIsYourDiet);
    on<UpdateAddCalsAgainToTarget>(_updateAddCalsAgainToTarget);
    on<UpdateAddYesterdayCals>(_updateAddYesterdayCals);
    on<UpdateTargetWeight>(_updateTargetWeight);
    on<NextStep>(_nextStep);
    on<PreviousStep>(_previousStep);
    on<SubmitOnboarding>(_submitOnboarding);
    on<RegisterScreenValidation>(_registerScreenValidation);
    on<RebuildFlow>(_rebuildFlow);
    on<ProcessingComplete>(_processingComplete);
  }

  // Helper method to build the flow based on current state
  static List<String> _buildFlow(OnboardingState state, bool isFromSettings) {
    if (isFromSettings) {
      return settingsScreensId;
    }
    return allScreens.where((screen) => screen.shouldShow == null || screen.shouldShow!(state)).map((screen) => screen.id).toList();
  }

  void _initializeBloc(InitializeBloc event, Emitter<OnboardingState> emit) {
    // Get saved values from SharedPreferences
    final currentStep = event.isFromSettings ? 0 : ShPH.getData(key: AppKeys.currentOnboardingStep);
    final gender = Gender.fromValue(ShPH.getData(key: AppKeys.gender));
    final exerciseFrequency = ExerciseFrequency.fromValue(ShPH.getData(key: AppKeys.exerciseFrequency));
    final whereDidYouHearOfUs = HeardAboutUsExtension.fromValue(ShPH.getData(key: AppKeys.whereDidYouHearOfUs));
    final hasUsedOtherApps = Choice.fromValue(ShPH.getData(key: AppKeys.hasUsedOtherApps));
    final height = ShPH.getData(key: AppKeys.height);
    final weight = ShPH.getData(key: AppKeys.weight);
    final addCalsAgain = ShPH.getData(key: AppKeys.addCalsAgain);
    final firstMeal = ShPH.getData(key: AppKeys.firstMeal);
    final secondMeal = ShPH.getData(key: AppKeys.secondMeal);
    final thirdMeal = ShPH.getData(key: AppKeys.thirdMeal);
    final birthDay = ShPH.getData(key: AppKeys.birthDay);
    final birthMonth = ShPH.getData(key: AppKeys.birthMonth);
    final birthYear = ShPH.getData(key: AppKeys.birthYear);
    final goal = Goal.fromValue(ShPH.getData(key: AppKeys.goal));
    final whatMakesYouDoesnotCommit = WhatMakesYouDoesnotCommitExtension.fromValue(ShPH.getData(key: AppKeys.whatMakesYouDoesnotCommit));
    final diet = DietExtension.fromValue(ShPH.getData(key: AppKeys.whatIsYourDiet));
    final achieve = AchieveExtension.fromValue(ShPH.getData(key: AppKeys.whatDoYouWantToAchieve));
    final targetWeight = ShPH.getData(key: AppKeys.targetWeight);
    final weightChangeRate = ShPH.getData(key: AppKeys.weightChangeRate);
    final cals = ShPH.getData(key: AppKeys.cals);
    final carbs = ShPH.getData(key: AppKeys.carbs);
    final fat = ShPH.getData(key: AppKeys.fat);
    final protien = ShPH.getData(key: AppKeys.protien);

    // Create initial state with saved values
    final initialState = state.copyWith(
      gender: gender,
      exerciseFrequency: exerciseFrequency,
      whereDidYouHearOfUs: whereDidYouHearOfUs,
      hasUsedOtherApps: hasUsedOtherApps,
      height: height,
      weight: weight,
      birthDay: birthDay,
      birthMonth: birthMonth,
      birthYear: birthYear,
      goal: goal,
      achieve: achieve,
      diet: diet,
      whatMakesYouDoesnotCommit: whatMakesYouDoesnotCommit,
      targetWeight: targetWeight,
      weightChangeRate: weightChangeRate,
      cals: cals,
      carbs: carbs,
      fat: fat,
      protein: protien,
      firstMeal: firstMeal,
      secondMeal: secondMeal,
      thirdMeal: thirdMeal,
      addCalsToTargetAgain: addCalsAgain,
      shouldShowInSettingsScreens: event.isFromSettings,
    );

    // Build flow based on initial state
    final flow = _buildFlow(initialState, event.isFromSettings);

    // Determine current flow index based on saved step
    int flowIndex = 0;
    if (currentStep != null && currentStep > 0) {
      flowIndex = currentStep < flow.length ? currentStep : 0;

      if (pageController.hasClients) {
        pageController.jumpToPage(flowIndex);
      }
    }

    emit(initialState.copyWith(
      flowScreenIds: flow,
      flowIndex: flowIndex,
    ));
  }

  void _nextStep(NextStep event, Emitter<OnboardingState> emit) {
    // If we're at the last screen, don't proceed
    if (state.flowIndex >= state.flowScreenIds.length - 1) return;

    // Calculate next index in the flow
    final nextIndex = state.flowIndex + 1;

    // Navigate to the next page
    pageController.animateToPage(
      nextIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    // Save current step to SharedPreferences
    ShPH.saveData(key: AppKeys.currentOnboardingStep, value: nextIndex);

    // Special handling for processing info screen (calculations)
    if (state.flowScreenIds.length > nextIndex && state.flowScreenIds[nextIndex] == 'processing_info') {
      log("Processing info step reached.");

      double cal;
      double bmr;
      double tdee;

      final age = DateTime.now().year - state.birthYear!;
      log("User age: $age");

      if (state.gender!.value == 'male') {
        bmr = (10 * state.weight!) + (6.25 * state.height!) - (5 * age) + 5;
      } else {
        bmr = (10 * state.weight!) + (6.25 * state.height!) - (5 * age) - 161;
      }

      log("BMR calculated: $bmr");

      tdee = bmr * state.exerciseFrequency!.val;
      log("TDEE calculated: $tdee (Exercise multiplier: ${state.exerciseFrequency!.val})");

      double adjustment = ((state.weightChangeRate ?? 1) * 7700) / 7;
      log("Adjustment (weekly): $adjustment");

      if (state.goal?.value == 'weight_gain') {
        cal = tdee + adjustment;
        log("Goal: Weight Gain → Calories increased to: $cal");
      } else if (state.goal?.value == 'weight_loss') {
        cal = tdee - adjustment;
        log("Goal: Weight Loss → Calories decreased to: $cal");
      } else {
        cal = tdee;
        log("Goal: Maintain → Calories stay the same: $cal");
      }

      final cals = cal.round();
      final carbs = ((cal * .50) / 4).round();
      final protein = ((cal * .25) / 4).round();
      final fat = ((cal * .25) / 9).round();

      log("Final Calorie breakdown → Calories: $cals | Carbs: $carbs | Protein: $protein | Fat: $fat");

      ShPH.saveData(key: AppKeys.cals, value: cals);
      ShPH.saveData(key: AppKeys.carbs, value: carbs);
      ShPH.saveData(key: AppKeys.protien, value: protein);
      ShPH.saveData(key: AppKeys.fat, value: fat);

      log("Data saved to shared preferences.");

      emit(
        state.copyWith(
          flowIndex: nextIndex,
          cals: cals,
          carbs: carbs,
          protein: protein,
          fat: fat,
        ),
      );

      log("State emitted with updated nutritional data.");
    } else {
      // Standard next step
      emit(state.copyWith(flowIndex: nextIndex));
    }
  }

  void _previousStep(PreviousStep event, Emitter<OnboardingState> emit) {
    // If we're at the first screen, don't go back
    if (state.flowIndex <= 0) return;

    // Calculate previous index in the flow
    final previousIndex = state.flowIndex - 1;

    // Navigate to the previous page
    pageController.animateToPage(
      previousIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    // Save current step to SharedPreferences
    ShPH.saveData(key: AppKeys.currentOnboardingStep, value: previousIndex);

    // Update state
    emit(state.copyWith(flowIndex: previousIndex));
  }

  void _updateGender(UpdateGender event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.gender, value: event.gender.value);
    emit(state.copyWith(gender: event.gender));
  }

  void _updateExerciseFrequency(UpdateExerciseFrequency event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.exerciseFrequency, value: event.frequency.activity);
    emit(state.copyWith(exerciseFrequency: event.frequency));
  }

  void _updateWhereDidYouHeardAboutUs(UpdateWhereDidYouHeardAboutUs event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.whereDidYouHearOfUs, value: event.heardAboutUs.key);
    emit(state.copyWith(whereDidYouHearOfUs: event.heardAboutUs));
  }

  void _updateHasUsedOtherApps(UpdateHasUsedOtherApps event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.hasUsedOtherApps, value: event.choice.value);
    emit(state.copyWith(hasUsedOtherApps: event.choice));
  }

  void _updateHeight(UpdateHeight event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.height, value: event.height);
    emit(state.copyWith(height: event.height));
  }

  void _updateWeight(UpdateWeight event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.weight, value: event.weight);
    emit(state.copyWith(weight: event.weight, targetWeight: event.weight.toDouble()));
  }

  void _updateBirthDay(UpdateBirthDay event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.birthDay, value: event.day);
    emit(state.copyWith(birthDay: event.day));
  }

  void _updateBirthMonth(UpdateBirthMonth event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.birthMonth, value: event.month);
    emit(state.copyWith(birthMonth: event.month));
  }

  void _updateBirthYear(UpdateBirthYear event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.birthYear, value: event.year);
    emit(state.copyWith(birthYear: event.year));
  }

  void _updateGoal(UpdateGoal event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.goal, value: event.goal.tdeeGoal);

    // Update state with new goal
    final updatedState = state.copyWith(goal: event.goal);

    // Rebuild flow based on new goal
    final newFlow = _buildFlow(updatedState, event.isFromSettings);

    // Emit updated state with new flow
    emit(updatedState.copyWith(flowScreenIds: newFlow));
  }

  void _updateMealtime(UpdateMealtime event, Emitter<OnboardingState> emit) {
    if (event.firstMeal != null) {
      ShPH.saveData(key: AppKeys.firstMeal, value: event.firstMeal);
      emit(state.copyWith(firstMeal: event.firstMeal));
    }
    if (event.secondMeal != null) {
      ShPH.saveData(key: AppKeys.secondMeal, value: event.secondMeal);
      emit(state.copyWith(secondMeal: event.secondMeal));
    }
    if (event.thirdMeal != null) {
      ShPH.saveData(key: AppKeys.thirdMeal, value: event.thirdMeal);
      emit(state.copyWith(thirdMeal: event.thirdMeal));
    }
  }

  void _updateWhatMakesYouDoesnotCommit(UpdateWhatMakesYouDoesnotCommit event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.whatMakesYouDoesnotCommit, value: event.whatMakesYouDoesnotCommit.localizedName);

    // Update state with new goal
    final updatedState = state.copyWith(whatMakesYouDoesnotCommit: event.whatMakesYouDoesnotCommit);

    // Rebuild flow based on new goal
    final newFlow = _buildFlow(updatedState, false);

    // Emit updated state with new flow
    emit(updatedState.copyWith(flowScreenIds: newFlow));
  }

  void _updateUpdateWhatIsYourDiet(UpdateWhatIsYourDiet event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.whatIsYourDiet, value: event.diet.key);

    // Emit updated state with new flow
    emit(state.copyWith(diet: event.diet));
  }

  void _updateWhatYouWantToAchieve(UpdateWhatYouWantToAchieve event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.whatYouWantToAchieve, value: event.achieve.key);
    emit(state.copyWith(achieve: event.achieve));
  }

  void _updateTargetWeight(UpdateTargetWeight event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.targetWeight, value: event.targetWeight);
    emit(state.copyWith(targetWeight: event.targetWeight));
  }

  void _updateWeightLossRate(UpdateWeightChangeRate event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.weightChangeRate, value: event.rate);
    emit(state.copyWith(weightChangeRate: event.rate));
  }

  void _rebuildFlow(RebuildFlow event, Emitter<OnboardingState> emit) {
    final newFlow = _buildFlow(state, false);
    emit(state.copyWith(flowScreenIds: newFlow));
  }

  void _updateAddCalsAgainToTarget(UpdateAddCalsAgainToTarget event, Emitter<OnboardingState> emit) {
    ShPH.saveData(key: AppKeys.addCalsAgain, value: event.value);

    emit(state.copyWith(addCalsToTargetAgain: event.value));
  }

  void _updateAddYesterdayCals(UpdateAddYesterdayCals event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(addYesterdayCalsToTarget: event.value));
  }

  Future<void> _submitOnboarding(SubmitOnboarding event, Emitter<OnboardingState> emit) async {
    emit(state.copyWith(status: OnboardingStatus.processing));
    final String mobileId = await MobileIdHelper.getMobileId();
    final String firebaseToken = await NotificationService().getToken();
    final List<Meal> meals = [
      Meal(type: "first", time: state.firstMeal),
      Meal(type: "second", time: state.secondMeal),
      Meal(type: "third", time: state.thirdMeal),
    ].where((meal) => meal.time != null).toList();

    final OnboardingModel onboardingModel = OnboardingModel(
      mobileId: mobileId,
      firebaseToken: firebaseToken,
      height: state.height,
      weight: state.weight,
      gender: state.gender!.value,
      birthdate:  DateTime(state.birthYear!, state.birthMonth!, state.birthDay!).toIso8601String(),
      activity: state.exerciseFrequency!.activity == "none" ? null : state.exerciseFrequency!.activity,
      tdeeGoal: state.goal!.tdeeGoal,
      targetWeight: state.targetWeight ?? state.weight?.toDouble(),
      weeklyTarget: state.weightChangeRate ?? 0.0,
      diet: state.diet!.key,
      triedAnotherApp: state.hasUsedOtherApps!.value == "yes" ? true : false,
      hearingAboutUs: state.whereDidYouHearOfUs?.key,
      goal: state.achieve!.key,
      meals: meals,
      caloriesAdded: state.addCalsToTargetAgain,
    );

    final SubmitOnboardingParams submitOnboardingParams = SubmitOnboardingParams(onboardingModel: onboardingModel);
    final result = await submitOnboardingUsecase(submitOnboardingParams);

    result.fold((l) {
      emit(state.copyWith(status: OnboardingStatus.error));
    }, (r) {
      localSaveUpdateUserUseCase.saveUpdateUserData(
        UserModel(
          weightChangeRate: state.weightChangeRate,
          gender: state.gender!.value,
          birthDate: DateTime(state.birthYear!, state.birthMonth!, state.birthDay!),
          height: state.height!.toString(),
          weight: state.weight!.toString(),
          activity: state.exerciseFrequency!.activity,
          targetWeight: state.targetWeight.toString(),
          weeklyTarget: state.weightChangeRate.toString(),
          goal: state.goal!.tdeeGoal,
          hearingAboutUs: state.whereDidYouHearOfUs?.localizedName ?? '',
          targetCalories: state.cals?.toString() ?? "",
          targetCarbs: state.carbs.toString(),
          targetFat: state.fat.toString(),
          targetProtein: state.protein.toString(),
          exerciseFrequency: state.exerciseFrequency?.activity ?? '',
        ),
        true,
      );
      emit(state.copyWith(status: OnboardingStatus.success));
    });
  }

  Future<void> _updateNutritionValues(UpdateNutritionValues event, Emitter<OnboardingState> emit) async {
    // emit(state.copyWith(status: OnboardingStatus.processing));
    if (event.calories != null) {
      ShPH.saveData(key: AppKeys.cals, value: event.calories);
    }
    if (event.carbs != null) {
      ShPH.saveData(key: AppKeys.carbs, value: event.carbs);
    }
    if (event.protein != null) {
      ShPH.saveData(key: AppKeys.protien, value: event.protein);
    }
    if (event.fat != null) {
      ShPH.saveData(key: AppKeys.fat, value: event.fat);
    }
    emit(state.copyWith(
      cals: event.calories ?? state.cals,
      carbs: event.carbs ?? state.carbs,
      protein: event.protein ?? state.protein,
      fat: event.fat ?? state.fat,
    ));
  }

  void _registerScreenValidation(RegisterScreenValidation event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(currentScreenValidation: event.validationFunction));
  }

  void _processingComplete(ProcessingComplete event, Emitter<OnboardingState> emit) {
    emit(state.copyWith(isButtonActive: true));
  }

  // @override
  // Future<void> close() {
  //   pageController.dispose();
  //   return super.close();
  // }
}
