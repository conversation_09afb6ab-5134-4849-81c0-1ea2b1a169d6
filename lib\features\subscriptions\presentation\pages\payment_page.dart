import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/extentions/navigation_extensions.dart';
import 'package:cal/common/widgets/toast_dialog.dart';
import 'package:cal/features/authentication/presentation/pages/authentication_page.dart';

import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cal/features/subscriptions/presentation/bloc/subscription_bloc.dart';
import 'package:cal/features/subscriptions/presentation/widgets/payment_plan_card.dart';
import 'package:cal/features/subscriptions/enums.dart';
import 'package:toastification/toastification.dart';

class PaymentPage extends StatefulWidget {
  const PaymentPage({super.key});

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextButton(
          onPressed: () {
            context.read<SubscriptionBloc>().add(const RestorePurchases());
          },
          child: Text(
            LocaleKeys.payment_restore.tr(),
            style: context.textTheme.bodyMedium,
          ),
        ),
      ),
      body: BlocListener<SubscriptionBloc, SubscriptionState>(
        listener: (context, state) {
          if (state.status == SubscriptionPurchaseStatus.purchased) {
            context.pushReplacement(const AuthenticationPage());
          } else if (state.status == SubscriptionPurchaseStatus.error) {
            ToastificationDialog.showToast(
              msg: state.errorMessage ?? '',
              context: context,
              type: ToastificationType.error,
            );
            Navigator.of(context).pushNamed("/exit_promo");
          }
        },
        child: BlocBuilder<SubscriptionBloc, SubscriptionState>(
          builder: (context, state) {
            final hasPlans = state.subscriptionPlans.isNotEmpty;

            return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [


                  // Subscription Plans
                  _buildSubscriptionPlans(context, state),

                  const SizedBox(height: 30),

                  // Purchase button
                  _buildPurchaseButton(context, state),                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSubscriptionPlans(BuildContext context, SubscriptionState state) {
    if (state.subscriptionPlans.isEmpty) {
      return Center(
        child: state.errorMessage != null && state.errorMessage!.isNotEmpty
            ? Text(
                state.errorMessage!,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              )
            : CircularProgressIndicator(
                color: context.primaryColor,
              ),
      );
    }

    // Use try-firstWhere to safely find plans without throwing an error
    final yearlyPlan = state.subscriptionPlans.cast<dynamic>().firstWhere(
          (plan) => plan.type == SubscriptionPlanType.yearly,
          orElse: () => null,
        );

    final monthlyPlan = state.subscriptionPlans.cast<dynamic>().firstWhere(
          (plan) => plan.type == SubscriptionPlanType.monthly,
          orElse: () => null,
        );

    final List<Widget> planCards = [];

    if (yearlyPlan != null) {
      planCards.add(Expanded(
        child: PaymentPlanCard(
          plan: yearlyPlan,
          isSelected: state.selectedPlan?.id == yearlyPlan.id,
          onTap: () {
            context.read<SubscriptionBloc>().add(SelectSubscriptionPlan(plan: yearlyPlan));
          },

        ),
      ));
    }

    if (monthlyPlan != null) {
      if (planCards.isNotEmpty) {
        planCards.add(const SizedBox(width: 16));
      }
      planCards.add(Expanded(
        child: PaymentPlanCard(
          plan: monthlyPlan,
          isSelected: state.selectedPlan?.id == monthlyPlan.id,
          onTap: () {
            context.read<SubscriptionBloc>().add(SelectSubscriptionPlan(plan: monthlyPlan));
          },
        ),
      ));
    }

    if (planCards.isEmpty) {
      return const Center(child: Text("No Plans Available"));
    }

    return Row(children: planCards);
  }

  Widget _buildPurchaseButton(BuildContext context, SubscriptionState state) {
    final isLoading = state.status == SubscriptionPurchaseStatus.loading;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading
            ? null
            : () {
                if (state.selectedPlan != null) {
                  context.read<SubscriptionBloc>().add(const PurchaseSubscription());
                } else {
                  ToastificationDialog.showToast(
                      msg: LocaleKeys.payment_choose_subscription_plan.tr(), context: context, type: ToastificationType.error);
                }
              },
        style: ElevatedButton.styleFrom(
          backgroundColor: context.primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                "Purchase",
                style: context.textTheme.titleSmall,
              ),
      ),
    );
  }
}
