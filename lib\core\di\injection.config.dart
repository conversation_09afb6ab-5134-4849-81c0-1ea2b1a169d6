// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:isar/isar.dart' as _i338;

import '../../features/home/<USER>/datasources/home_remote_datasource.dart'
    as _i278;
import '../../features/home/<USER>/datasources/local_food_data_source.dart'
    as _i737;
import '../../features/home/<USER>/repositories/exercise_repository_impl.dart'
    as _i1053;
import '../../features/home/<USER>/repositories/food_repository_impl.dart'
    as _i884;
import '../../features/home/<USER>/repositories/home_repository_impl.dart'
    as _i76;
import '../../features/home/<USER>/repositories/exercise_repository.dart'
    as _i422;
import '../../features/home/<USER>/repositories/food_repository.dart' as _i12;
import '../../features/home/<USER>/repositories/home_repository.dart' as _i0;
import '../../features/home/<USER>/usecases/delete_exercise_usecase.dart'
    as _i392;
import '../../features/home/<USER>/usecases/get_daily_exercises_usecase.dart'
    as _i35;
import '../../features/home/<USER>/usecases/get_daily_user_data_usecase.dart'
    as _i1066;
import '../../features/home/<USER>/usecases/update_daily_user_data_usecase.dart'
    as _i258;
import '../../features/home/<USER>/bloc/nutrition_bloc/bloc/nutrition_bloc.dart'
    as _i686;
import '../../features/home/<USER>/bloc/recent_activity_bloc.dart' as _i7;
import '../../features/main/data/repo/main_repo_impl.dart' as _i818;
import '../../features/main/domain/repo/main_repo.dart' as _i587;
import '../../features/main/presentation/bloc/main_bloc.dart' as _i1014;
import '../../features/onboarding/data/datasource/onboarding_remote_datasource.dart'
    as _i36;
import '../../features/onboarding/data/repositories/onboarding_repository_imp.dart'
    as _i389;
import '../../features/onboarding/domain/repositories/onboarding_repository.dart'
    as _i430;
import '../../features/onboarding/domain/usecases/get_user_local_user_case.dart'
    as _i305;
import '../../features/onboarding/domain/usecases/local_delete_clear_user_use_case.dart'
    as _i942;
import '../../features/onboarding/domain/usecases/local_save_update_user_use_case.dart'
    as _i950;
import '../../features/onboarding/domain/usecases/submit_onboarding_usecase.dart'
    as _i933;
import '../../features/onboarding/presentation/bloc/onboarding_bloc.dart'
    as _i792;
import '../../features/quick_actions/exercise/data/datasources/exercise_local_datasource.dart'
    as _i449;
import '../../features/quick_actions/exercise/data/datasources/exercise_remote_datasource.dart'
    as _i867;
import '../../features/quick_actions/exercise/data/repositories/exercise_repository_impl.dart'
    as _i178;
import '../../features/quick_actions/exercise/domain/repositories/exercise_repository.dart'
    as _i207;
import '../../features/quick_actions/exercise/domain/services/calorie_calculation_service.dart'
    as _i984;
import '../../features/quick_actions/exercise/domain/usecases/save_exercise_ai_usecase.dart'
    as _i491;
import '../../features/quick_actions/exercise/domain/usecases/save_exercise_usecase.dart'
    as _i735;
import '../../features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart'
    as _i932;
import '../../features/quick_actions/food_database/data/datasources/food_remote_data_source.dart'
    as _i56;
import '../../features/quick_actions/food_database/data/datasources/local_food_database_data_source.dart'
    as _i50;
import '../../features/quick_actions/food_database/data/repositories/food_database_repository_impl.dart'
    as _i1028;
import '../../features/quick_actions/food_database/domain/repositories/food_database_repository.dart'
    as _i257;
import '../../features/quick_actions/food_database/domain/usecases/add_selected_food_usecase.dart'
    as _i569;
import '../../features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart'
    as _i310;
import '../../features/quick_actions/food_database/domain/usecases/delete_meal_usecase.dart'
    as _i104;
import '../../features/quick_actions/food_database/domain/usecases/get_database_food_usecase.dart'
    as _i386;
import '../../features/quick_actions/food_database/domain/usecases/get_my_meals_usecase.dart'
    as _i982;
import '../../features/quick_actions/food_database/domain/usecases/get_recent_food_usecase.dart'
    as _i147;
import '../../features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart'
    as _i654;
import '../../features/quick_actions/food_database/domain/usecases/remove_selected_food_usecase.dart'
    as _i97;
import '../../features/quick_actions/food_database/domain/usecases/save_and_create_meal_usecase.dart'
    as _i1058;
import '../../features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart'
    as _i104;
import '../../features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart'
    as _i469;
import '../../features/quick_actions/scan_food/data/datasources/scan_food_remote_datasource.dart'
    as _i184;
import '../../features/quick_actions/scan_food/data/repositories/scan_food_repository_impl.dart'
    as _i343;
import '../../features/quick_actions/scan_food/domain/repositories/scan_food_repository.dart'
    as _i1002;
import '../../features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart'
    as _i634;
import '../../features/quick_actions/scan_food/domain/usecases/scan_barcode_use_case.dart'
    as _i945;
import '../../features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart'
    as _i420;
import '../datasources/streak_local_data_source.dart' as _i84;
import '../datasources/user_local_data_source.dart' as _i889;
import '../isar_initialization.dart' as _i502;
import '../local_models/daily_data_model/daily_user_info_service.dart' as _i321;
import '../network/http_client.dart' as _i1069;
import '../network/interceptor.dart' as _i159;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    await gh.factoryAsync<_i338.Isar>(
      () => registerModule.isar,
      preResolve: true,
    );
    gh.factory<_i984.CalorieCalculationService>(
        () => _i984.CalorieCalculationService());
    gh.factory<_i569.AddSelectedFoodUseCase>(
        () => _i569.AddSelectedFoodUseCase());
    gh.factory<_i97.RemoveSelectedFoodUseCase>(
        () => _i97.RemoveSelectedFoodUseCase());
    gh.lazySingleton<_i321.DailyUserInfoService>(
        () => _i321.DailyUserInfoService());
    gh.lazySingleton<_i159.LoggerInterceptor>(() => _i159.LoggerInterceptor());
    gh.factory<_i449.ExerciseLocalDataSource>(
        () => _i449.ExerciseLocalDataSourceImpl(gh<_i338.Isar>()));
    gh.factory<_i84.StreakLocalDataSource>(
        () => _i84.StreakLocalDataSource(gh<_i338.Isar>()));
    gh.factory<_i889.UserLocalDataSource>(
        () => _i889.UserLocalDataSource(gh<_i338.Isar>()));
    gh.factory<_i737.LocalFoodDataSource>(
        () => _i737.LocalFoodDataSource(gh<_i338.Isar>()));
    gh.factory<_i50.LocalFoodDatabaseDataSource>(
        () => _i50.LocalFoodDatabaseDataSource(gh<_i338.Isar>()));
    gh.lazySingleton<_i1069.HTTPClient>(() => _i1069.DioClient());
    gh.lazySingleton<_i278.HomeLocalDataSource>(
        () => _i278.HomeLocalDataSourceImpl(isar: gh<_i338.Isar>()));
    gh.factory<_i422.HomeExerciseRepository>(() =>
        _i1053.HomeExerciseRepositoryImpl(
            exerciseLocalDataSource: gh<_i449.ExerciseLocalDataSource>()));
    gh.lazySingleton<_i0.HomeRepository>(() => _i76.HomeRepositoryImpl(
        localDataSource: gh<_i278.HomeLocalDataSource>()));
    gh.factory<_i587.MainRepo>(() =>
        _i818.MainRepoImpl(localDataSource: gh<_i84.StreakLocalDataSource>()));
    gh.lazySingleton<_i56.FoodRemoteDataSource>(
        () => _i56.FoodRemoteDataSource(dioClient: gh<_i1069.HTTPClient>()));
    gh.lazySingleton<_i36.OnboardingRemoteDatasource>(() =>
        _i36.OnboardingRemoteDatasource(httpClient: gh<_i1069.HTTPClient>()));
    gh.lazySingleton<_i184.ScanFoodRemoteDataSource>(() =>
        _i184.ScanFoodRemoteDataSource(httpClient: gh<_i1069.HTTPClient>()));
    gh.factory<_i12.FoodRepository>(() => _i884.FoodRepositoryImpl(
        localDataSource: gh<_i737.LocalFoodDataSource>()));
    gh.factory<_i867.ExerciseRemoteDataSource>(() =>
        _i867.ExerciseRemoteDataSourceImpl(
            httpClient: gh<_i1069.HTTPClient>()));
    gh.factory<_i1066.GetDailyUserDataUseCase>(() =>
        _i1066.GetDailyUserDataUseCase(repository: gh<_i0.HomeRepository>()));
    gh.factory<_i258.UpdateDailyUserDataUseCase>(() =>
        _i258.UpdateDailyUserDataUseCase(repository: gh<_i0.HomeRepository>()));
    gh.lazySingleton<_i1002.ScanFoodRepository>(() =>
        _i343.ScanFoodRepositoryImpl(gh<_i184.ScanFoodRemoteDataSource>()));
    gh.factory<_i686.NutritionBloc>(() => _i686.NutritionBloc(
        getDailyUserDataUseCase: gh<_i1066.GetDailyUserDataUseCase>()));
    gh.factory<_i1014.MainBloc>(() => _i1014.MainBloc(gh<_i587.MainRepo>()));
    gh.lazySingleton<_i634.AnalyzeFoodUseCase>(
        () => _i634.AnalyzeFoodUseCase(gh<_i1002.ScanFoodRepository>()));
    gh.factory<_i207.ExerciseRepository>(() => _i178.ExerciseRepositoryImpl(
          remoteDataSource: gh<_i867.ExerciseRemoteDataSource>(),
          localDataSource: gh<_i449.ExerciseLocalDataSource>(),
        ));
    gh.factory<_i392.DeleteExerciseUseCase>(
        () => _i392.DeleteExerciseUseCase(gh<_i422.HomeExerciseRepository>()));
    gh.factory<_i35.GetDailyExercisesUseCase>(() =>
        _i35.GetDailyExercisesUseCase(gh<_i422.HomeExerciseRepository>()));
    gh.factory<_i257.FoodDatabaseRepository>(
        () => _i1028.FoodDatabaseRepositoryImpl(
              gh<_i56.FoodRemoteDataSource>(),
              localDataSource: gh<_i50.LocalFoodDatabaseDataSource>(),
            ));
    gh.lazySingleton<_i430.OnboardingRepository>(
        () => _i389.OnboardingRepositoryImp(
              onboardingRemoteDatasource: gh<_i36.OnboardingRemoteDatasource>(),
              userLocalDataSource: gh<_i889.UserLocalDataSource>(),
            ));
    gh.lazySingleton<_i945.ScanBarcodeUseCase>(() => _i945.ScanBarcodeUseCase(
        scanFoodRepository: gh<_i1002.ScanFoodRepository>()));
    gh.factory<_i491.SaveExerciseAiUseCase>(
        () => _i491.SaveExerciseAiUseCase(gh<_i207.ExerciseRepository>()));
    gh.factory<_i735.SaveExerciseUseCase>(
        () => _i735.SaveExerciseUseCase(gh<_i207.ExerciseRepository>()));
    gh.singleton<_i7.RecentActivityBloc>(() => _i7.RecentActivityBloc(
          foodRepository: gh<_i12.FoodRepository>(),
          exerciseRepository: gh<_i422.HomeExerciseRepository>(),
          getDailyUserDataUseCase: gh<_i1066.GetDailyUserDataUseCase>(),
          updateDailyUserDataUseCase: gh<_i258.UpdateDailyUserDataUseCase>(),
          getDailyExercisesUseCase: gh<_i35.GetDailyExercisesUseCase>(),
          deleteExerciseUseCase: gh<_i392.DeleteExerciseUseCase>(),
        ));
    gh.lazySingleton<_i310.CreateMealUseCase>(() => _i310.CreateMealUseCase(
        foodDatabaseRepository: gh<_i257.FoodDatabaseRepository>()));
    gh.lazySingleton<_i654.PostMealToLog>(() => _i654.PostMealToLog(
        foodDatabaseRepository: gh<_i257.FoodDatabaseRepository>()));
    gh.lazySingleton<_i104.SearchMealsUseCase>(() => _i104.SearchMealsUseCase(
        foodDatabaseRepository: gh<_i257.FoodDatabaseRepository>()));
    gh.factory<_i420.ScanFoodBloc>(() => _i420.ScanFoodBloc(
          analyzeFoodUseCase: gh<_i634.AnalyzeFoodUseCase>(),
          scanBarcodeUseCase: gh<_i945.ScanBarcodeUseCase>(),
          recentActivityBloc: gh<_i7.RecentActivityBloc>(),
        ));
    gh.factory<_i932.ExerciseBloc>(() => _i932.ExerciseBloc(
          saveExerciseUseCase: gh<_i735.SaveExerciseUseCase>(),
          saveExerciseAiUseCase: gh<_i491.SaveExerciseAiUseCase>(),
          recentActivityBloc: gh<_i7.RecentActivityBloc>(),
        ));
    gh.lazySingleton<_i305.GetUserLocalUserCase>(() =>
        _i305.GetUserLocalUserCase(
            onboardingRepository: gh<_i430.OnboardingRepository>()));
    gh.lazySingleton<_i942.LocalDeleteClearUserUseCase>(() =>
        _i942.LocalDeleteClearUserUseCase(
            onboardingRepository: gh<_i430.OnboardingRepository>()));
    gh.lazySingleton<_i950.LocalSaveUpdateUserUseCase>(() =>
        _i950.LocalSaveUpdateUserUseCase(
            onboardingRepository: gh<_i430.OnboardingRepository>()));
    gh.lazySingleton<_i933.SubmitOnboardingUsecase>(() =>
        _i933.SubmitOnboardingUsecase(
            onboardingRepository: gh<_i430.OnboardingRepository>()));
    gh.factory<_i1058.SaveAndCreateMealUseCase>(
        () => _i1058.SaveAndCreateMealUseCase(
              repository: gh<_i257.FoodDatabaseRepository>(),
              createMealUseCase: gh<_i310.CreateMealUseCase>(),
            ));
    gh.factory<_i104.DeleteMealUseCase>(() => _i104.DeleteMealUseCase(
        repository: gh<_i257.FoodDatabaseRepository>()));
    gh.factory<_i386.GetDatabaseFoodUsecase>(() => _i386.GetDatabaseFoodUsecase(
        repository: gh<_i257.FoodDatabaseRepository>()));
    gh.factory<_i982.GetMyMealsUsecase>(() => _i982.GetMyMealsUsecase(
        repository: gh<_i257.FoodDatabaseRepository>()));
    gh.factory<_i147.GetRecentFoodUsecase>(() => _i147.GetRecentFoodUsecase(
        repository: gh<_i257.FoodDatabaseRepository>()));
    gh.factory<_i792.OnboardingBloc>(() => _i792.OnboardingBloc(
          submitOnboardingUsecase: gh<_i933.SubmitOnboardingUsecase>(),
          localDeleteClearUserUseCase: gh<_i942.LocalDeleteClearUserUseCase>(),
          localSaveUpdateUserUseCase: gh<_i950.LocalSaveUpdateUserUseCase>(),
          getUserLocalUserCase: gh<_i305.GetUserLocalUserCase>(),
        ));
    gh.factory<_i469.FoodDatabaseBloc>(() => _i469.FoodDatabaseBloc(
          foodDatabaseRepository: gh<_i257.FoodDatabaseRepository>(),
          searchMealsUseCase: gh<_i104.SearchMealsUseCase>(),
          postMealToLog: gh<_i654.PostMealToLog>(),
          createMealUseCase: gh<_i310.CreateMealUseCase>(),
          addSelectedFoodUseCase: gh<_i569.AddSelectedFoodUseCase>(),
          removeSelectedFoodUseCase: gh<_i97.RemoveSelectedFoodUseCase>(),
          saveAndCreateMealUseCase: gh<_i1058.SaveAndCreateMealUseCase>(),
          deleteMealUseCase: gh<_i104.DeleteMealUseCase>(),
        ));
    return this;
  }
}

class _$RegisterModule extends _i502.RegisterModule {}
