import 'dart:async';

import 'package:cal/core/config/app_config.dart';
import 'package:cal/features/subscriptions/data/models/subscription_plan_model.dart';
import 'package:cal/features/subscriptions/domain/entities/subscription_plan_entity.dart';
import 'package:cal/features/subscriptions/enums.dart';
import 'package:dio/dio.dart';
import 'package:dartz/dartz.dart';
import 'package:in_app_purchase/in_app_purchase.dart' as iap;

abstract class SubscriptionRemoteDataSource {
  /// Check if the store is available
  Future<Either<String, bool>> isStoreAvailable();

  /// Get subscription plans from the store
  Future<Either<String, List<SubscriptionPlanModel>>> getSubscriptionPlans();

  /// Purchase a subscription
  Future<Either<String, void>> purchaseSubscription(SubscriptionPlanEntity plan);

  /// Restore previously purchased subscriptions
  Future<Either<String, void>> restorePurchases();

  /// Complete a purchase
  Future<Either<String, void>> completePurchase(iap.PurchaseDetails purchaseDetails);

  /// Verify an Apple purchase with the backend and get the expiry date.
  Future<Either<String, DateTime>> verifyApplePurchase(iap.PurchaseDetails purchaseDetails);

  /// Get purchase updates stream
  Stream<iap.PurchaseDetails> get purchaseUpdates;
}

class SubscriptionRemoteDataSourceImpl implements SubscriptionRemoteDataSource {
  final iap.InAppPurchase _inAppPurchase = iap.InAppPurchase.instance;
  final Dio _dio;

  final String _baseUrl = AppConfig.baseUrl;
  StreamController<iap.PurchaseDetails>? _purchaseController;
  StreamSubscription<List<iap.PurchaseDetails>>? _purchaseSubscription;
  List<iap.ProductDetails> _products = [];

  SubscriptionRemoteDataSourceImpl({required Dio dio}) : _dio = dio {
    _purchaseController = StreamController<iap.PurchaseDetails>.broadcast();
    _setupPurchaseStream();
  }

  void _setupPurchaseStream() {
    _purchaseSubscription = _inAppPurchase.purchaseStream.listen(
      (List<iap.PurchaseDetails> purchaseDetailsList) {
        for (final iap.PurchaseDetails purchaseDetails in purchaseDetailsList) {
          _purchaseController?.add(purchaseDetails);
        }
      },
      onDone: () {
        _purchaseSubscription?.cancel();
      },
      onError: (error) {
        // Handle error
      },
    );
  }

  @override
  Future<Either<String, bool>> isStoreAvailable() async {
    try {
      final bool available = await _inAppPurchase.isAvailable();
      return Right(available);
    } catch (e) {
      return Left('Failed to check store availability: $e');
    }
  }

  @override
  Future<Either<String, List<SubscriptionPlanModel>>> getSubscriptionPlans() async {
    try {
      final Set<String> productIds = {'monthly_sub', 'quarterly_sub', 'yearly_sub'};

      final iap.ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);

      if (response.error != null) {
        return Left('Failed to load subscription plans: ${response.error}');
      }

      if (response.productDetails.isEmpty) {
        // No subscription plans available from the store
        return const Left('No subscription plans available. Please try again later.');
      }

      _products = response.productDetails;

      // Map store products to subscription plans
      final List<SubscriptionPlanModel> plans = _products.map((iap.ProductDetails product) {
        SubscriptionPlanType type;
        List<String> features = [];

        if (product.id == 'monthly_sub') {
          type = SubscriptionPlanType.monthly;
          features = ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics'];
        } else if (product.id == 'quarterly_sub') {
          type = SubscriptionPlanType.quarterly;
          features = ['Personalized workout plans', 'Nutrition tracking', 'Progress analytics', 'Premium workout videos'];
        } else {
          type = SubscriptionPlanType.yearly;
          features = [
            'Personalized workout plans',
            'Nutrition tracking',
            'Progress analytics',
            'Premium workout videos',
            'Priority support'
          ];
        }

        return SubscriptionPlanModel.fromProductDetails(product, type, features);
      }).toList();

      return Right(plans);
    } catch (e) {
      return Left('Failed to load subscription plans: $e');
    }
  }

  @override
  Future<Either<String, void>> purchaseSubscription(SubscriptionPlanEntity plan) async {
    try {
      final productDetails = _products.firstWhere(
        (p) => p.id == plan.id,
        orElse: () => throw Exception('Product not found. Please fetch products first.'),
      );

      final iap.PurchaseParam purchaseParam = iap.PurchaseParam(
        productDetails: productDetails,
      );

      // For both subscriptions and non-consumable one-time purchases.
      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      return const Right(null);
    } catch (e) {
      return Left('Failed to initiate purchase: $e');
    }
  }

  @override
  Future<Either<String, void>> restorePurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
      return const Right(null);
    } catch (e) {
      return Left('Failed to restore purchases: $e');
    }
  }

  @override
  Future<Either<String, void>> completePurchase(iap.PurchaseDetails purchaseDetails) async {
    try {
      await _inAppPurchase.completePurchase(purchaseDetails);
      return const Right(null);
    } catch (e) {
      return Left('Failed to complete purchase: $e');
    }
  }

  @override
  Future<Either<String, DateTime>> verifyApplePurchase(iap.PurchaseDetails purchaseDetails) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/payment/apple',
        data: {
          'receipt-data': purchaseDetails.verificationData.serverVerificationData,
        },
      );

      // With dio, a successful response (2xx) will land here.
      final responseBody = response.data;
      if (responseBody['status'] == 'success' && responseBody['data'] != null) {
        final String expiresDateMs = responseBody['data']['expires_date_ms'];
        final expiryDate = DateTime.fromMillisecondsSinceEpoch(int.parse(expiresDateMs));
        // Optional: Check if product ID from backend matches the purchase
        if (responseBody['data']['product_id'] != purchaseDetails.productID) {
          // ignore: prefer_const_constructors
          return Left('Product ID mismatch during verification.');
        }
        return Right(expiryDate);
      } else {
        final message = responseBody['message'] ?? 'Unknown validation error';
        return Left('Backend validation failed: $message');
      }
    } on DioException catch (e) {
      String errorMessage = 'An error occurred during purchase verification.';
      if (e.response?.data != null && e.response!.data['message'] is String) {
        errorMessage = 'Backend Error: ${e.response!.data['message']} (Status: ${e.response?.statusCode})';
      } else if (e.message != null) {
        errorMessage = 'Network Error: ${e.message}';
      }
      return Left(errorMessage);
    } catch (e) {
      return Left('An error occurred during purchase verification: $e');
    }
  }

  @override
  Stream<iap.PurchaseDetails> get purchaseUpdates => _purchaseController!.stream;

  void dispose() {
    _purchaseSubscription?.cancel();
    _purchaseController?.close();
  }
}
