import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/features/subscriptions/presentation/bloc/subscription_bloc.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FreeHookPage extends StatelessWidget {
  const FreeHookPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextButton(
          onPressed: () {
            context.read<SubscriptionBloc>().add(const RestorePurchases());
          },
          child: Text(
            LocaleKeys.payment_restore.tr(),
            style: context.textTheme.bodyLarge,
          ),
        ),
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Top: Always visible text
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
                  child: Text(
                    LocaleKeys.payment_free_trial_start_message.tr(),
                    textAlign: TextAlign.center,
                    style: context.textTheme.headlineMedium!.copyWith(fontWeight: FontWeight.w600),
                  ),
                ),

                Expanded(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: 200,
                      maxHeight: constraints.maxHeight * 0.8,
                    ),
                    child: const AppImage.asset(Assets.imagesMockup),
                  ),
                ),

                // Bottom: Always visible
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        LocaleKeys.payment_no_payment_due.tr(),
                        style: context.textTheme.bodyLarge,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pushNamed('/trial_explainer');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.primaryColor,
                            foregroundColor: context.onPrimaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            LocaleKeys.payment_try_for_free.tr(),
                            style: context.textTheme.titleSmall!.copyWith(color: context.onPrimaryColor, fontWeight: FontWeight.w700),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        LocaleKeys.payment_yearly_price_info.tr(namedArgs: {
                          'price': '\$29.99',
                          'monthlyPrice': '\$2.49',
                        }),
                        style: context.textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
